import { PixelRatio, useWindowDimensions } from "react-native";
import { useFontScale } from "./font-scale-context";

interface ResponsiveFontOptions {
  baseFontSize: number;
  maxScale?: number;
  minFontSize?: number;
  maxFontSize?: number;
}

export const useResponsiveFont = ({
  baseFontSize,
  maxScale = 1.3,
  minFontSize = 12,
  maxFontSize = 24,
}: ResponsiveFontOptions) => {
  const { width } = useWindowDimensions();

  let fontScaleContext;
  try {
    fontScaleContext = useFontScale();
  } catch {
    fontScaleContext = null;
  }

  const systemFontScale = PixelRatio.getFontScale();

  const calculateFontSize = () => {
    let fontSize = baseFontSize;

    if (fontScaleContext) {
      fontSize = fontScaleContext.getControlledFontSize(baseFontSize);
    } else {
      const effectiveScale = Math.min(systemFontScale, maxScale);
      fontSize = baseFontSize * effectiveScale;
    }

    if (width < 350) {
      fontSize = fontSize * 0.9;
    }

    fontSize = Math.max(minFontSize, Math.min(maxFontSize, fontSize));

    return Math.round(fontSize);
  };

  return {
    fontSize: calculateFontSize(),
    isLargeFontScale: systemFontScale > 1.3,
    systemFontScale,
  };
};
