import React, { createContext, useContext, ReactNode } from "react";
import { PixelRatio } from "react-native";

interface FontScaleContextType {
  maxFontScale: number;
  getControlledFontSize: (baseFontSize: number) => number;
  isLargeFontScale: boolean;
}

const FontScaleContext = createContext<FontScaleContextType | undefined>(
  undefined
);

interface FontScaleProviderProps {
  children: ReactNode;
  maxFontScale?: number;
  authMode?: boolean; // Stricter limits for auth screens
}

export const FontScaleProvider: React.FC<FontScaleProviderProps> = ({
  children,
  maxFontScale,
  authMode = false,
}) => {
  const systemFontScale = PixelRatio.getFontScale();

  const defaultMaxScale = authMode ? 1.2 : 1.3;
  const effectiveMaxScale = maxFontScale || defaultMaxScale;

  const isLargeFontScale = systemFontScale > 1.3;

  const getControlledFontSize = (baseFontSize: number): number => {
    if (systemFontScale <= effectiveMaxScale) {
      return baseFontSize * systemFontScale;
    }

    return baseFontSize * effectiveMaxScale;
  };

  const value: FontScaleContextType = {
    maxFontScale: effectiveMaxScale,
    getControlledFontSize,
    isLargeFontScale,
  };

  return (
    <FontScaleContext.Provider value={value}>
      {children}
    </FontScaleContext.Provider>
  );
};

export const useFontScale = (): FontScaleContextType => {
  const context = useContext(FontScaleContext);
  if (!context) {
    throw new Error("useFontScale must be used within a FontScaleProvider");
  }
  return context;
};

export const getControlledFontSizeStatic = (
  baseFontSize: number,
  maxScale: number = 1.3
): number => {
  const systemFontScale = PixelRatio.getFontScale();
  if (systemFontScale <= maxScale) {
    return baseFontSize * systemFontScale;
  }
  return baseFontSize * maxScale;
};
