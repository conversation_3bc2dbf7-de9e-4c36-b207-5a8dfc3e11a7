import React from 'react';
import { View, PixelRatio } from 'react-native';
import { Text } from '~/components/ui/text';
import { ControlledText } from '~/components/ui/controlled-text';
import { useFontScale } from '~/lib/font-scale-context';
import { useResponsiveFont } from '~/lib/use-responsive-font';

/**
 * Test component to verify font scaling solution
 * This component demonstrates different font scaling approaches
 */
export const FontScaleTest: React.FC = () => {
  const systemFontScale = PixelRatio.getFontScale();
  
  // Try to get font scale context
  let fontScaleContext;
  try {
    fontScaleContext = useFontScale();
  } catch {
    fontScaleContext = null;
  }

  const responsiveFont = useResponsiveFont({
    baseFontSize: 16,
    maxScale: 1.2,
  });

  return (
    <View style={{ padding: 20, gap: 16 }}>
      <Text allowFontScaling={false} style={{ fontWeight: 'bold', fontSize: 18 }}>
        Font Scale Test
      </Text>
      
      <Text allowFontScaling={false}>
        System Font Scale: {systemFontScale.toFixed(2)}
      </Text>
      
      {fontScaleContext && (
        <>
          <Text allowFontScaling={false}>
            Max Font Scale: {fontScaleContext.maxFontScale}
          </Text>
          <Text allowFontScaling={false}>
            Is Large Font Scale: {fontScaleContext.isLargeFontScale ? 'Yes' : 'No'}
          </Text>
        </>
      )}

      <View style={{ gap: 8 }}>
        <Text allowFontScaling={false} style={{ fontWeight: 'bold' }}>
          Text Scaling Examples:
        </Text>
        
        {/* No scaling */}
        <Text allowFontScaling={false}>
          No scaling: This text will not scale with system font size
        </Text>
        
        {/* Limited scaling */}
        <Text allowFontScaling={true} maxFontSizeMultiplier={1.2}>
          Limited scaling (1.2x): This text scales but is capped at 1.2x
        </Text>
        
        {/* Controlled text with explicit font size */}
        <ControlledText baseFontSize={16} maxFontScale={1.1}>
          Controlled text (16px base, 1.1x max): Precise font size control
        </ControlledText>
        
        {/* Responsive font hook */}
        <Text allowFontScaling={false} style={{ fontSize: responsiveFont.fontSize }}>
          Responsive font hook: {responsiveFont.fontSize}px
        </Text>
      </View>

      <View style={{ gap: 8 }}>
        <Text allowFontScaling={false} style={{ fontWeight: 'bold' }}>
          Layout Test:
        </Text>
        
        <View style={{ 
          flexDirection: 'row', 
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: 12,
          backgroundColor: '#f0f0f0',
          borderRadius: 8,
        }}>
          <Text allowFontScaling={true} maxFontSizeMultiplier={1.1}>
            Label
          </Text>
          <Text allowFontScaling={false} style={{ fontWeight: 'bold' }}>
            Value
          </Text>
        </View>
      </View>
    </View>
  );
};
