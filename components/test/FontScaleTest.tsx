import React, { useState } from "react";
import { View, PixelRatio } from "react-native";
import { Text } from "~/components/ui/text";
import { AuthText } from "~/components/ui/auth-text";
import { Input } from "~/components/ui/input";
import { But<PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "~/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  type Option,
} from "~/components/ui/select";
import { showSuccessToast, showErrorToast } from "~/components/toast";
import { useFontScale } from "~/lib/font-scale-context";

/**
 * Test component to verify the comprehensive font scaling solution
 * This component demonstrates that font scaling is disabled across all components
 */
export const FontScaleTest: React.FC = () => {
  const systemFontScale = PixelRatio.getFontScale();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectValue, setSelectValue] = useState<Option>();

  // Get font scale context
  let fontScaleContext;
  try {
    fontScaleContext = useFontScale();
  } catch {
    fontScaleContext = null;
  }

  return (
    <View style={{ padding: 20, gap: 16 }}>
      <Text style={{ fontWeight: "bold", fontSize: 18 }}>
        Font Scale Test - Complete Solution
      </Text>

      <Text>System Font Scale: {systemFontScale.toFixed(2)}</Text>

      {fontScaleContext && (
        <>
          <Text>Max Font Scale: {fontScaleContext.maxFontScale}</Text>
          <Text>
            Should Disable Font Scaling:{" "}
            {fontScaleContext.shouldDisableFontScaling ? "Yes" : "No"}
          </Text>
          <Text>
            Is Large Font Scale:{" "}
            {fontScaleContext.isLargeFontScale ? "Yes" : "No"}
          </Text>
        </>
      )}

      <View style={{ gap: 12, marginTop: 16 }}>
        <Text style={{ fontWeight: "bold" }}>Component Tests:</Text>

        {/* Regular Text - should not scale */}
        <Text>
          ✅ Regular Text: This text will NOT scale with device font size
        </Text>

        {/* AuthText - extra strict */}
        <AuthText>✅ AuthText: This auth text will NEVER scale</AuthText>

        {/* Input field - should not scale */}
        <Input placeholder="✅ Input: This input will not scale" />

        {/* Button - should not scale */}
        <Button
          label="✅ Button: This button text will not scale"
          onPress={() => console.log("Button pressed")}
        />

        {/* Override example - allow limited scaling */}
        <Text allowFontScaling={true} maxFontSizeMultiplier={1.1}>
          ⚠️ Override: This text can scale up to 1.1x (if explicitly allowed)
        </Text>

        {/* Dialog test */}
        <Button label="✅ Test Dialog" onPress={() => setDialogOpen(true)} />

        {/* Select test */}
        <Select value={selectValue} onValueChange={setSelectValue}>
          <SelectTrigger>
            <SelectValue placeholder="✅ Test Select Component" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="option1" label="Option 1 - Fixed font size">
              Option 1 - Fixed font size
            </SelectItem>
            <SelectItem value="option2" label="Option 2 - Fixed font size">
              Option 2 - Fixed font size
            </SelectItem>
            <SelectItem value="option3" label="Option 3 - Fixed font size">
              Option 3 - Fixed font size
            </SelectItem>
          </SelectContent>
        </Select>

        {/* Toast test */}
        <View style={{ flexDirection: "row", gap: 8 }}>
          <Button
            label="✅ Success Toast"
            onPress={() => showSuccessToast("Success! Fixed font size")}
            variant="secondary"
          />
          <Button
            label="✅ Error Toast"
            onPress={() => showErrorToast("Error! Fixed font size")}
            variant="secondary"
          />
        </View>
      </View>

      {/* Dialog component */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>✅ Dialog Test</DialogTitle>
            <DialogDescription>
              This dialog text should have fixed font size regardless of device
              settings.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button label="Close" variant="secondary" />
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <View
        style={{
          marginTop: 20,
          padding: 16,
          backgroundColor: "#f0f0f0",
          borderRadius: 8,
        }}
      >
        <Text style={{ fontWeight: "bold", marginBottom: 8 }}>
          Expected Behavior:
        </Text>
        <Text>• All text should appear at consistent sizes</Text>
        <Text>• No layout breaks regardless of device font size</Text>
        <Text>• Components maintain proper alignment</Text>
        <Text>• Only override text can scale (and only slightly)</Text>
      </View>

      <View
        style={{
          marginTop: 16,
          padding: 16,
          backgroundColor: systemFontScale > 1.3 ? "#ffebee" : "#e8f5e8",
          borderRadius: 8,
        }}
      >
        <Text style={{ fontWeight: "bold", marginBottom: 8 }}>
          Device Font Scale Status:
        </Text>
        <Text>
          {systemFontScale > 1.3
            ? `🔴 Large font scale detected (${systemFontScale.toFixed(
                2
              )}x) - Our solution is protecting the layout!`
            : `🟢 Normal font scale (${systemFontScale.toFixed(
                2
              )}x) - Layout is stable`}
        </Text>
      </View>
    </View>
  );
};
