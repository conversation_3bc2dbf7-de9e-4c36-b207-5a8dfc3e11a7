import * as Slot from "@rn-primitives/slot";
import type { SlottableTextProps, TextRef } from "@rn-primitives/types";
import * as React from "react";
import { Text as RNText, PixelRatio } from "react-native";
import { cn } from "~/lib/utils";

type AuthTextProps = SlottableTextProps & {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isHeader?: boolean;
  maxFontScale?: number; // Maximum font scale for this text
};

/**
 * Auth-specific Text component that completely disables font scaling on login/auth screens
 * This component ensures auth screens always use our exact font sizes for layout stability
 */
const AuthText = React.forwardRef<TextRef, AuthTextProps>(
  (
    {
      className,
      asChild = false,
      isHeader,
      accessibilityLabel,
      accessibilityHint,
      maxFontScale = 1.0, // Force no scaling for auth screens
      ...props
    },
    ref
  ) => {
    const Component = asChild ? Slot.Text : RNText;

    return (
      <Component
        className={cn("text-base text-foreground web:select-text", className)}
        ref={ref}
        accessibilityRole={isHeader ? "header" : "text"}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        allowFontScaling={false} // Always disable font scaling for auth screens
        maxFontSizeMultiplier={1.0} // Force exact font sizes
        {...props}
      />
    );
  }
);
AuthText.displayName = "AuthText";

export { AuthText };
