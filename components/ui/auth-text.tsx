import * as Slot from "@rn-primitives/slot";
import type { SlottableTextProps, TextRef } from "@rn-primitives/types";
import * as React from "react";
import { Text as RNText, PixelRatio } from "react-native";
import { cn } from "~/lib/utils";

type AuthTextProps = SlottableTextProps & {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isHeader?: boolean;
  maxFontScale?: number; // Maximum font scale for this text
};

/**
 * Auth-specific Text component that prevents font scaling issues on login/auth screens
 * This component enforces a maximum font scale to prevent layout breaks
 */
const AuthText = React.forwardRef<TextRef, AuthTextProps>(
  (
    {
      className,
      asChild = false,
      isHeader,
      accessibilityLabel,
      accessibilityHint,
      maxFontScale = 1.2, // Default max scale for auth screens
      ...props
    },
    ref
  ) => {
    const Component = asChild ? Slot.Text : RNText;
    
    // Get system font scale and determine if we should limit it
    const systemFontScale = PixelRatio.getFontScale();
    const shouldLimitFontScale = systemFontScale > maxFontScale;
    
    return (
      <Component
        className={cn(
          "text-base text-foreground web:select-text",
          className
        )}
        ref={ref}
        accessibilityRole={isHeader ? "header" : "text"}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        allowFontScaling={!shouldLimitFontScale} // Disable scaling if system scale is too large
        maxFontSizeMultiplier={maxFontScale} // Set maximum multiplier
        {...props}
      />
    );
  }
);
AuthText.displayName = "AuthText";

export { AuthText };
