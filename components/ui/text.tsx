import * as Slot from "@rn-primitives/slot";
import type { SlottableTextProps, TextRef } from "@rn-primitives/types";
import * as React from "react";
import { Text as RNText, StyleSheet, PixelRatio } from "react-native";
import { cn } from "~/lib/utils";
import {
  useFontScale,
  getControlledFontSizeStatic,
} from "~/lib/font-scale-context";

type ExtendedTextProps = SlottableTextProps & {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isHeader?: boolean;
  allowFontScaling?: boolean; // Control whether to allow font scaling
  maxFontSizeMultiplier?: number; // Maximum font size multiplier
};

const TextClassContext = React.createContext<string | undefined>(undefined);

const Text = React.forwardRef<TextRef, ExtendedTextProps>(
  (
    {
      className,
      asChild = false,
      isHeader,
      accessibilityLabel,
      accessibilityHint,
      allowFontScaling = false, // Default to false to prevent scaling issues
      maxFontSizeMultiplier = 1.3, // Default max multiplier
      ...props
    },
    ref
  ) => {
    const textClass = React.useContext(TextClassContext);
    const Component = asChild ? Slot.Text : RNText;

    // Try to get font scale context, but don't fail if not available
    let fontScaleContext;
    try {
      fontScaleContext = useFontScale();
    } catch {
      fontScaleContext = null;
    }

    // Determine if we should allow font scaling based on context and props
    const shouldAllowFontScaling =
      allowFontScaling &&
      (!fontScaleContext || !fontScaleContext.isLargeFontScale);

    return (
      <Component
        className={cn(
          "text-base text-foreground web:select-text",
          textClass,
          className
        )}
        ref={ref}
        accessibilityRole={isHeader ? "header" : "text"}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        allowFontScaling={shouldAllowFontScaling}
        maxFontSizeMultiplier={maxFontSizeMultiplier}
        {...props}
      />
    );
  }
);
Text.displayName = "Text";

export { Text, TextClassContext };
