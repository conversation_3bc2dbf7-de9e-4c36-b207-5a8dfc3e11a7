import { Image } from "expo-image";
import { Link } from "expo-router";
import { PropsWithChildren } from "react";
import { ImageBackground, View, PixelRatio } from "react-native";
import { Text } from "~/components/ui/text";

export const LoginBackground = ({ children }: PropsWithChildren) => {
  // Adjust logo size and positioning based on font scale
  const fontScale = PixelRatio.getFontScale();
  const isLargeFontScale = fontScale > 1.3;

  // Reduce logo size and adjust positioning for large font scales
  const logoHeight = isLargeFontScale ? 60 : 80;
  const logoWidth = isLargeFontScale ? 75 : 100;
  const logoTop = isLargeFontScale ? 80 : 100;

  return (
    <ImageBackground
      source={require("../../../assets/images/login.png")}
      resizeMode={"cover"}
      style={{ flex: 1, width: "100%" }}
      resizeMethod="auto"
    >
      <Image
        source={require("../../../assets/images/login-logo.svg")}
        style={{
          height: logoHeight,
          width: logoWidth,
          display: "flex",
          position: "relative",
          top: logoTop,
          left: "50%",
          transform: [{ translateX: -logoWidth / 2 }],
        }}
        contentFit="contain"
      />
      {children}
      <View
        style={{
          position: "absolute",
          bottom: isLargeFontScale ? 15 : 25,
          left: 0,
          right: 0,
          padding: isLargeFontScale ? 12 : 16,
        }}
      >
        <Text
          className="pl-6 pr-6 text-white mb-3 font-bold"
          allowFontScaling={true}
          maxFontSizeMultiplier={1.2}
        >
          Contact your Upace administrator if you do not have an account.
        </Text>
        <Text
          className="pl-6 pr-6 text-white font-bold"
          allowFontScaling={true}
          maxFontSizeMultiplier={1.2}
        >
          By logging in, you are agreeing to our{" "}
          <Link
            className="underline"
            href={
              "https://rachel-koretsky.squarespace.com/terms-and-privacy-new"
            }
          >
            terms of service and privacy policy.
          </Link>
        </Text>
      </View>
    </ImageBackground>
  );
};
