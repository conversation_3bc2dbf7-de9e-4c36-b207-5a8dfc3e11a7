import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Tabs } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import FontAwesome6 from "@expo/vector-icons/FontAwesome6";

import { useColorScheme } from "~/lib/useColorScheme";
import { FEATURES, useFeatureEnabled } from "~/modules/hooks/useFeatureEnabled";
import { useAnalytics } from "~/modules/hooks/useAnalytics";

export default function TabLayout() {
  const { isDarkColorScheme } = useColorScheme();
  const isFeatureEnabled = useFeatureEnabled(FEATURES.SUB_MANAGER_MODULE);
  const { trackEvent, EVENTS } = useAnalytics();

  // Function to track tab changes
  const handleTabPress = (tabName: string) => {
    trackEvent(EVENTS.TAB_CHANGE, { tab_name: tabName });
  };

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: "blue",
        tabBarAccessibilityLabel: "Tab Navigation",
        tabBarAllowFontScaling: false, // Prevent font scaling to maintain tab layout
        tabBarLabelStyle: { fontSize: 12 }, // Force specific font size for tab labels
      }}
      screenListeners={{
        tabPress: (e) => {
          // Extract the tab name from the route
          const tabName = e.target?.split("-")[0] || "unknown";
          handleTabPress(tabName);
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Home",
          headerShown: false,
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          tabBarAccessibilityLabel: "Home tab",
          tabBarIcon: ({ color, size }) => (
            <FontAwesome
              size={size}
              name="home"
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Home icon"
            />
          ),
        }}
        listeners={{
          focus: () => handleTabPress("Home"),
        }}
      />
      <Tabs.Screen
        name="classes"
        options={{
          title: "Classes",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Classes tab",
          tabBarIcon: ({ color }) => (
            <FontAwesome6
              name="users"
              size={20}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Classes icon"
            />
          ),
        }}
      />

      <Tabs.Screen
        name="subs"
        options={{
          href: isFeatureEnabled
            ? {
                pathname: "/(classes)/subs",
              }
            : null,
          title: "Sub Mgt",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Substitute Management tab",
          tabBarIcon: ({ color }) => (
            <MaterialIcons
              name="swap-calls"
              size={27}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Substitute Management icon"
            />
          ),
        }}
      />

      <Tabs.Screen
        name="appointments"
        options={{
          title: "Appts",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Appointments tab",
          tabBarIcon: ({ color }) => (
            <FontAwesome
              name="calendar"
              size={24}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Appointments icon"
            />
          ),
        }}
      />

      {/* <Tabs.Screen
        name="swipecards"
        options={{
          title: "Swipe",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false, // Hide the header completely
          headerTransparent: true, // Make header transparent
          tabBarIcon: ({ color }) => (
            <Ionicons
              name="card"
              size={24}
              color={isDarkColorScheme ? "white" : color}
            />
          ),
        }}
      /> */}
    </Tabs>
  );
}
