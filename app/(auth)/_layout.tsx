import { Stack } from "expo-router";
import { FontScaleProvider } from "~/lib/font-scale-context";

export default function LoginLayout() {
  return (
    <FontScaleProvider authMode={true} maxFontScale={1.2}>
      <Stack>
        <Stack.Screen
          name="sign-in"
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="forgot-password"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="[email]"
          options={{
            headerShown: false,
          }}
        />
      </Stack>
    </FontScaleProvider>
  );
}
