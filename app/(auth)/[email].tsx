import * as React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { LoginBackground } from "~/components/modules/login/login-bg";
import LoginCard from "~/components/modules/login/login-card";
import { Text } from "~/components/ui/text";
import { ResetPassword } from "~/modules/login/reset-password";

export default function ResetPasswordPage() {
  return (
    <LoginBackground>
      <KeyboardAwareScrollView
        bottomOffset={10}
        contentContainerStyle={{
          gap: 16,
          paddingBottom: 30,
          flex: 1,
        }}
      >
        <LoginCard>
          <Text
            className="text-3xl font-extrabold text-[#069CC3]"
            allowFontScaling={true}
            maxFontSizeMultiplier={1.1}
          >
            FORGOT PASSWORD?
          </Text>

          <Text
            className="mb-6 mt-1 dark:text-black"
            allowFontScaling={true}
            maxFontSizeMultiplier={1.2}
          >
            Enter your pin received in your email to reset your password
          </Text>
          <View className="w-full">
            <ResetPassword />
          </View>
        </LoginCard>
      </KeyboardAwareScrollView>
    </LoginBackground>
  );
}
