import * as React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { LoginBackground } from "~/components/modules/login/login-bg";
import LoginCard from "~/components/modules/login/login-card";
import { Text } from "~/components/ui/text";
import { useSession } from "~/modules/login/auth-provider";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import { LoginForm } from "~/modules/login/login";

export default function Login() {
  const { signIn, isError, error, isLoading } = useSession();

  return (
    <ScreenTracker screenName="Login">
      <LoginBackground>
        <KeyboardAwareScrollView
          bottomOffset={10}
          contentContainerStyle={{
            gap: 16,
            paddingBottom: 30,
            flex: 1,
          }}
        >
          <LoginCard>
            <Text
              className="text-3xl font-extrabold text-[#069CC3]"
              allowFontScaling={true}
              maxFontSizeMultiplier={1.1}
            >
              WELCOME
            </Text>

            <Text
              className="mb-6 mt-1 dark:text-black"
              allowFontScaling={true}
              maxFontSizeMultiplier={1.2}
            >
              Login with your email address associated with your admin account.
            </Text>
            <View className="w-full">
              <LoginForm
                handleLogin={signIn}
                isLoading={isLoading}
                isError={isError}
                errorMessage={error?.message}
              />
            </View>
          </LoginCard>
        </KeyboardAwareScrollView>
      </LoginBackground>
    </ScreenTracker>
  );
}
