# Font Scaling Solution

This document describes the implementation of a comprehensive font scaling solution to prevent app layout breaks when users increase their system font size.

## Problem

When users increase their system font size (accessibility feature), the app layout can break, especially on login and authentication screens where precise layout is critical.

## Solution Overview

The solution implements a multi-layered approach:

1. **FontScaleProvider Context** - Global font scaling control
2. **Enhanced Text Component** - Text component with built-in scaling limits
3. **Responsive Layout Adjustments** - Dynamic layout adjustments based on font scale
4. **Input Field Protection** - Prevent font scaling in form inputs to maintain layout

## Implementation Details

### 1. FontScaleProvider (`lib/font-scale-context.tsx`)

A React context provider that:
- Detects system font scale using `PixelRatio.getFontScale()`
- Provides controlled font size calculation
- Supports different max scales for different app sections
- Offers stricter limits for auth screens (`authMode: true`)

```tsx
// Global app wrapper (max scale 1.3)
<FontScaleProvider maxFontScale={1.3}>
  {/* App content */}
</FontScaleProvider>

// Auth screens wrapper (max scale 1.2)
<FontScaleProvider authMode={true} maxFontScale={1.2}>
  {/* Auth content */}
</FontScaleProvider>
```

### 2. Enhanced Text Component (`components/ui/text.tsx`)

The main Text component now includes:
- `allowFontScaling` prop (default: false)
- `maxFontSizeMultiplier` prop (default: 1.3)
- Automatic detection of large font scales
- Integration with FontScaleProvider

```tsx
<Text 
  allowFontScaling={true}
  maxFontSizeMultiplier={1.2}
>
  This text will scale but with limits
</Text>
```

### 3. Controlled Text Component (`components/ui/controlled-text.tsx`)

Alternative text component for precise font size control:
- `baseFontSize` prop for explicit size control
- `maxFontScale` prop for per-component limits
- Fallback support when context is unavailable

### 4. Layout Adjustments

#### Login Card (`components/modules/login/login-card.tsx`)
- Reduces card width and padding when font scale is large
- Maintains visual hierarchy while preventing overflow

#### Login Background (`components/modules/login/login-bg.tsx`)
- Adjusts logo size and positioning
- Reduces bottom padding for large font scales
- Applies controlled scaling to footer text

### 5. Input Protection (`components/ui/input.tsx`)

Input fields have `allowFontScaling={false}` to:
- Maintain consistent form layout
- Prevent input field overflow
- Preserve button and form alignment

## Usage Guidelines

### For New Components

1. **Use the enhanced Text component** with appropriate scaling settings:
```tsx
// For body text (allow some scaling)
<Text allowFontScaling={true} maxFontSizeMultiplier={1.2}>
  Body content
</Text>

// For headings (limited scaling)
<Text allowFontScaling={true} maxFontSizeMultiplier={1.1}>
  Heading
</Text>

// For UI elements (no scaling)
<Text allowFontScaling={false}>
  Button label
</Text>
```

2. **Use the responsive font hook** for dynamic sizing:
```tsx
const { fontSize, isLargeFontScale } = useResponsiveFont({
  baseFontSize: 16,
  maxScale: 1.2,
});
```

### For Layout Components

1. **Check for large font scales** and adjust layouts:
```tsx
const fontScale = PixelRatio.getFontScale();
const isLargeFontScale = fontScale > 1.3;

const padding = isLargeFontScale ? 12 : 16;
```

2. **Use FontScaleProvider** for section-specific limits:
```tsx
// Stricter limits for critical UI sections
<FontScaleProvider maxFontScale={1.1}>
  <CriticalUIComponent />
</FontScaleProvider>
```

## Configuration

### Font Scale Limits

- **Global app**: 1.3x maximum
- **Auth screens**: 1.2x maximum  
- **Critical UI**: 1.1x maximum
- **Input fields**: No scaling

### Breakpoints

- **Large font scale**: > 1.3x system scale
- **Small screen**: < 350px width
- **Landscape mode**: width > height

## Testing

To test the font scaling solution:

1. **iOS**: Settings > Display & Brightness > Text Size
2. **Android**: Settings > Display > Font size
3. **Simulator**: Device > Accessibility Inspector > Font Size

Test scenarios:
- Default font size
- Large font size (1.5x)
- Extra large font size (2x+)
- Different screen sizes
- Landscape orientation

## Benefits

1. **Prevents layout breaks** on auth screens
2. **Maintains accessibility** with controlled scaling
3. **Preserves visual hierarchy** in critical UI areas
4. **Responsive to different screen sizes**
5. **Backward compatible** with existing components

## Future Enhancements

1. **Dynamic breakpoints** based on content
2. **Per-component scaling profiles**
3. **Advanced layout algorithms** for complex components
4. **User preference storage** for font scaling settings
